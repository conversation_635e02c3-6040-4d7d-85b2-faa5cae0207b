package com.diary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.diary.entity.Diary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DiaryMapper extends BaseMapper<Diary> {
    
    /**
     * 分页查询用户的日记
     */
    IPage<Diary> selectUserDiaries(Page<Diary> page, @Param("userId") Long userId);
    
    /**
     * 分页查询公开的日记（随机排序）
     */
    IPage<Diary> selectPublicDiaries(Page<Diary> page);
    
    /**
     * 增加浏览次数
     */
    int incrementViewCount(@Param("diaryId") Long diaryId);
    
    /**
     * 增加点赞数
     */
    int incrementLikeCount(@Param("diaryId") Long diaryId);
    
    /**
     * 减少点赞数
     */
    int decrementLikeCount(@Param("diaryId") Long diaryId);
} 