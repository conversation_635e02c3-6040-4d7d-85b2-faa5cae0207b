import React, { useEffect, useState } from 'react';
import { 
  Card, 
  List, 
  Button, 
  Popconfirm, 
  message, 
  Empty, 
  Tag, 
  Space,
  Pagination 
} from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  PlusOutlined 
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getMyDiariesAsync, deleteDiaryAsync } from '../store/slices/diarySlice';
import { MOOD_OPTIONS } from '../utils/constants';
import dayjs from 'dayjs';
import './MyDiaries.css';

const MyDiaries = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { myDiaries, loading } = useSelector((state) => state.diary);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    fetchDiaries();
  }, [currentPage, pageSize]);

  const fetchDiaries = () => {
    dispatch(getMyDiariesAsync({ current: currentPage, size: pageSize }));
  };

  const handleDelete = async (id) => {
    try {
      await dispatch(deleteDiaryAsync(id)).unwrap();
      message.success('删除成功');
      fetchDiaries();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const getMoodInfo = (mood) => {
    return MOOD_OPTIONS.find(option => option.value === mood) || {};
  };

  const renderDiaryItem = (item) => (
    <List.Item
      key={item.id}
      actions={[
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => navigate(`/diary/${item.id}`)}
        >
          查看
        </Button>,
        <Button
          type="text"
          icon={<EditOutlined />}
          onClick={() => navigate(`/diary/edit/${item.id}`)}
        >
          编辑
        </Button>,
        <Popconfirm
          title="确定要删除这篇日记吗？"
          onConfirm={() => handleDelete(item.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>,
      ]}
    >
      <List.Item.Meta
        title={
          <div className="diary-title">
            <span>{item.title || '无标题'}</span>
            <Space>
              {item.isPublic ? (
                <Tag color="green">公开</Tag>
              ) : (
                <Tag color="orange">私密</Tag>
              )}
              {item.mood && (
                <Tag color={getMoodInfo(item.mood).color}>
                  {getMoodInfo(item.mood).label}
                </Tag>
              )}
            </Space>
          </div>
        }
        description={
          <div className="diary-meta">
            <div className="diary-content">
              {item.content.length > 100 
                ? `${item.content.substring(0, 100)}...` 
                : item.content
              }
            </div>
            <div className="diary-info">
              <Space split={<span>•</span>}>
                <span>{dayjs(item.createdAt).format('YYYY-MM-DD HH:mm')}</span>
                {item.weather && <span>天气：{item.weather}</span>}
                {item.location && <span>位置：{item.location}</span>}
                <span>浏览：{item.viewCount}</span>
                <span>点赞：{item.likeCount}</span>
              </Space>
            </div>
          </div>
        }
      />
    </List.Item>
  );

  return (
    <div className="my-diaries">
      <Card
        title="我的日记"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/diary/new')}
          >
            写新日记
          </Button>
        }
      >
        {myDiaries.records.length === 0 ? (
          <Empty
            description="还没有写过日记"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/diary/new')}
            >
              写第一篇日记
            </Button>
          </Empty>
        ) : (
          <>
            <List
              loading={loading}
              dataSource={myDiaries.records}
              renderItem={renderDiaryItem}
              pagination={false}
            />
            
            <div className="pagination-wrapper">
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={myDiaries.total}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
                onChange={handlePageChange}
              />
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default MyDiaries;
