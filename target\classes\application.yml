server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: diary-backend

  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DB_URL:*********************************************************************************************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:root} # 推荐通过环境变量DB_PASSWORD注入
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置（可选）
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath:mapper/*.xml

# 日志配置
logging:
  level:
    com.diary: debug
    org.springframework.web: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# JWT配置
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789} # 推荐通过环境变量JWT_SECRET注入，默认值用于开发
  expiration: 86400000 # 24小时
