.App {
  min-height: 100vh;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ant Design 样式覆盖 */
.ant-layout {
  background: #f5f5f5;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-btn {
  border-radius: 6px;
}

.ant-input,
.ant-input-password,
.ant-select-selector,
.ant-picker {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-card {
    margin: 8px;
  }

  .ant-layout-content {
    padding: 16px 8px;
  }
}
