import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { useSelector } from "react-redux";
import Layout from "./components/Layout";
import ProtectedRoute from "./components/ProtectedRoute";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Home from "./pages/Home";
import MyDiaries from "./pages/MyDiaries";
import DiaryForm from "./pages/DiaryForm";
import DiaryDetail from "./pages/DiaryDetail";
import Profile from "./pages/Profile";
import "./App.css";

function App() {
  const { isAuthenticated } = useSelector((state) => state.auth);

  return (
    <div className="App">
      <Routes>
        {/* 公开路由 */}
        <Route
          path="/login"
          element={isAuthenticated ? <Navigate to="/" replace /> : <Login />}
        />
        <Route
          path="/register"
          element={isAuthenticated ? <Navigate to="/" replace /> : <Register />}
        />

        {/* 受保护的路由 */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout>
                <Home />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/my-diaries"
          element={
            <ProtectedRoute>
              <Layout>
                <MyDiaries />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/diary/new"
          element={
            <ProtectedRoute>
              <Layout>
                <DiaryForm />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/diary/edit/:id"
          element={
            <ProtectedRoute>
              <Layout>
                <DiaryForm />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/diary/:id"
          element={
            <ProtectedRoute>
              <Layout>
                <DiaryDetail />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/profile"
          element={
            <ProtectedRoute>
              <Layout>
                <Profile />
              </Layout>
            </ProtectedRoute>
          }
        />

        {/* 默认重定向 */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </div>
  );
}

export default App;
