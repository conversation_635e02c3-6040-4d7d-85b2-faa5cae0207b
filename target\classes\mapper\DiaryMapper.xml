<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.diary.mapper.DiaryMapper">

    <!-- 分页查询用户的日记 -->
    <select id="selectUserDiaries" resultType="com.diary.entity.Diary">
        SELECT * FROM diaries 
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
    </select>

    <!-- 分页查询公开的日记（随机排序） -->
    <select id="selectPublicDiaries" resultType="com.diary.entity.Diary">
        SELECT * FROM diaries 
        WHERE is_public = 1
        ORDER BY RAND()
    </select>

    <!-- 增加浏览次数 -->
    <update id="incrementViewCount">
        UPDATE diaries 
        SET view_count = view_count + 1 
        WHERE id = #{diaryId}
    </update>

    <!-- 增加点赞数 -->
    <update id="incrementLikeCount">
        UPDATE diaries 
        SET like_count = like_count + 1 
        WHERE id = #{diaryId}
    </update>

    <!-- 减少点赞数 -->
    <update id="decrementLikeCount">
        UPDATE diaries 
        SET like_count = GREATEST(like_count - 1, 0) 
        WHERE id = #{diaryId}
    </update>

</mapper> 