import React from 'react';
import { Layout as AntLayout, Menu, Dropdown, Avatar, Button, message } from 'antd';
import { 
  HomeOutlined, 
  BookOutlined, 
  UserOutlined, 
  LogoutOutlined,
  PlusOutlined 
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { logout } from '../store/slices/authSlice';
import './Layout.css';

const { Header, Content } = AntLayout;

const Layout = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  const handleLogout = () => {
    dispatch(logout());
    message.success('已退出登录');
    navigate('/login');
  };

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/my-diaries',
      icon: <BookOutlined />,
      label: '我的日记',
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => navigate('/profile'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <AntLayout className="layout">
      <Header className="header">
        <div className="logo" onClick={() => navigate('/')}>
          📖 我的日记
        </div>
        
        <Menu
          theme="dark"
          mode="horizontal"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={({ key }) => navigate(key)}
          className="nav-menu"
        />

        <div className="header-right">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/diary/new')}
            className="new-diary-btn"
          >
            写日记
          </Button>
          
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            arrow
          >
            <div className="user-info">
              <Avatar icon={<UserOutlined />} />
              <span className="username">{user?.nickname || user?.username}</span>
            </div>
          </Dropdown>
        </div>
      </Header>
      
      <Content className="content">
        {children}
      </Content>
    </AntLayout>
  );
};

export default Layout;
