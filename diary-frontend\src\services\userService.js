import api from '../utils/api';

// 用户相关API服务

// 用户登录
export const login = async (loginData) => {
  return await api.post('/user/login', loginData);
};

// 用户注册
export const register = async (registerData) => {
  return await api.post('/user/register', registerData);
};

// 获取用户信息
export const getUserProfile = async () => {
  return await api.get('/user/profile');
};

// 更新用户信息
export const updateUserProfile = async (userData) => {
  return await api.put('/user/profile', userData);
};
