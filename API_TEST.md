# API 测试文档

## 测试环境
- 服务地址: http://localhost:8080
- 基础路径: /api

## 已测试的接口

### 1. 测试接口
**GET** `/api/test/hello`

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": "Hello, <PERSON> Backend is running!"
}
```

**测试命令:**
```bash
curl http://localhost:8080/api/test/hello
```

### 2. 用户注册接口
**POST** `/api/user/register`

**请求体:**
```json
{
  "username": "testuser",
  "password": "123456",
  "confirmPassword": "123456",
  "nickname": "测试用户",
  "email": "<EMAIL>",
  "gender": 1,
  "country": "中国",
  "province": "北京",
  "city": "北京"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": 3,
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "gender": 1,
    "country": "中国",
    "province": "北京",
    "city": "北京",
    "status": 1,
    "createdAt": "2024-01-01T10:00:00",
    "updatedAt": "2024-01-01T10:00:00"
  }
}
```

**测试命令:**
```powershell
Invoke-WebRequest -Uri "http://localhost:8080/api/user/register" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"username":"testuser","password":"123456","confirmPassword":"123456","nickname":"测试用户","email":"<EMAIL>","gender":1,"country":"中国","province":"北京","city":"北京"}'
```

### 3. 用户登录接口
**POST** `/api/user/login`

**请求体:**
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "email": "<EMAIL>",
      "gender": 1,
      "country": "中国",
      "province": "北京",
      "city": "北京",
      "status": 1,
      "createdAt": "2024-01-01T10:00:00",
      "updatedAt": "2024-01-01T10:00:00"
    }
  }
}
```

**测试命令:**
```powershell
Invoke-WebRequest -Uri "http://localhost:8080/api/user/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"username":"admin","password":"123456"}'
```

### 4. 获取公开日记列表
**GET** `/api/diary/public`

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "userId": 1,
        "title": "Beautiful Day",
        "content": "Today is a beautiful day, feeling great!",
        "mood": 5,
        "weather": "Sunny",
        "location": "Beijing",
        "isPublic": 1,
        "viewCount": 0,
        "likeCount": 0,
        "createdAt": "2024-01-01T10:00:00",
        "updatedAt": "2024-01-01T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

**测试命令:**
```powershell
Invoke-WebRequest -Uri "http://localhost:8080/api/diary/public" -Method GET
```

## 需要认证的接口

以下接口需要有效的JWT令牌（在Authorization头中携带Bearer token）：

### 5. 获取用户信息
**GET** `/api/user/profile`
**Headers:** `Authorization: Bearer <token>`

### 6. 更新用户信息
**PUT** `/api/user/profile`
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "nickname": "新昵称",
  "email": "<EMAIL>",
  "gender": 1,
  "country": "中国",
  "province": "上海",
  "city": "上海"
}
```

### 7. 创建日记
**POST** `/api/diary`
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "title": "我的日记",
  "content": "今天很开心！",
  "mood": 5,
  "weather": "晴天",
  "location": "北京",
  "isPublic": 1
}
```

### 8. 获取我的日记列表
**GET** `/api/diary/my`
**Headers:** `Authorization: Bearer <token>`

### 9. 获取日记详情
**GET** `/api/diary/{id}`
**Headers:** `Authorization: Bearer <token>`

### 10. 更新日记
**PUT** `/api/diary/{id}`
**Headers:** `Authorization: Bearer <token>`

### 11. 删除日记
**DELETE** `/api/diary/{id}`
**Headers:** `Authorization: Bearer <token>`

## 测试步骤

1. **启动应用**
   ```bash
   mvn spring-boot:run
   ```

2. **测试基础接口**
   ```bash
   curl http://localhost:8080/api/test/hello
   ```

3. **测试公开接口**
   ```powershell
   Invoke-WebRequest -Uri "http://localhost:8080/api/diary/public" -Method GET
   ```

4. **测试注册接口**
   ```powershell
   Invoke-WebRequest -Uri "http://localhost:8080/api/user/register" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"username":"testuser","password":"123456","confirmPassword":"123456","nickname":"测试用户","email":"<EMAIL>","gender":1,"country":"中国","province":"北京","city":"北京"}'
   ```

5. **测试登录接口**
   ```powershell
   Invoke-WebRequest -Uri "http://localhost:8080/api/user/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"username":"admin","password":"123456"}'
   ```

6. **使用返回的token测试需要认证的接口**
   ```powershell
   $token = "your_jwt_token_here"
   Invoke-WebRequest -Uri "http://localhost:8080/api/user/profile" -Method GET -Headers @{"Authorization"="Bearer $token"}
   ```

## 注意事项

1. 注册和登录接口不需要认证
2. 需要认证的接口需要先通过登录获取JWT令牌
3. 数据库需要先初始化（执行database/init.sql）
4. 确保MySQL服务正在运行
5. 确保应用配置文件中的数据库连接信息正确
6. 默认测试账号：
   - 用户名：admin，密码：123456
   - 用户名：testuser，密码：123456

## 状态码说明

- **200**: 成功
- **400**: 请求参数错误
- **401**: 未授权（需要登录）
- **403**: 禁止访问（权限不足）
- **404**: 资源不存在
- **500**: 服务器内部错误 