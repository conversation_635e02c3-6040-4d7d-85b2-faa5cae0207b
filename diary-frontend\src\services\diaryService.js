import api from '../utils/api';

// 日记相关API服务

// 获取公开日记列表
export const getPublicDiaries = async (params = {}) => {
  const { current = 1, size = 10 } = params;
  return await api.get(`/diary/public?current=${current}&size=${size}`);
};

// 获取我的日记列表
export const getMyDiaries = async (params = {}) => {
  const { current = 1, size = 10 } = params;
  return await api.get(`/diary/my?current=${current}&size=${size}`);
};

// 获取日记详情
export const getDiaryDetail = async (id) => {
  return await api.get(`/diary/${id}`);
};

// 创建日记
export const createDiary = async (diaryData) => {
  return await api.post('/diary', diaryData);
};

// 更新日记
export const updateDiary = async (id, diaryData) => {
  return await api.put(`/diary/${id}`, diaryData);
};

// 删除日记
export const deleteDiary = async (id) => {
  return await api.delete(`/diary/${id}`);
};
