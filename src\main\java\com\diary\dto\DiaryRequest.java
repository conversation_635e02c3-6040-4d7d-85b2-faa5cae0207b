package com.diary.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class DiaryRequest {
    
    private String title;
    
    @NotBlank(message = "日记内容不能为空")
    private String content;
    
    private Integer mood;
    
    private String weather;
    
    private String location;
    
    @NotNull(message = "是否公开不能为空")
    private Integer isPublic;
} 