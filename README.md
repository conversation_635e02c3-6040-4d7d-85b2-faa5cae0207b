# Web网站日记应用后端

这是一个基于Spring Boot的Web网站日记应用后端服务。

## 技术栈

- **后端框架**: Spring Boot 2.7.14
- **安全框架**: Spring Security + JWT
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis-Plus 3.5.3.1
- **项目管理**: Maven
- **其他**: Redis (可选)

## 功能特性

### 用户功能
- ✅ 用户注册
- ✅ 用户登录
- ✅ 用户信息管理
- ✅ JWT令牌认证

### 日记功能
- ✅ 创建日记
- ✅ 查看日记详情
- ✅ 获取我的日记列表
- ✅ 获取公开日记列表
- ✅ 编辑日记
- ✅ 删除日记
- ✅ 日记权限控制（公开/私密）

### 互动功能
- ✅ 点赞功能
- ✅ 评论功能（已设计，待实现）

## 项目结构

```
diary-backend/
├── src/main/java/com/diary/
│   ├── DiaryApplication.java          # 主启动类
│   ├── config/                        # 配置类
│   ├── controller/                    # 控制器层
│   │   ├── UserController.java        # 用户控制器
│   │   ├── DiaryController.java       # 日记控制器
│   │   └── TestController.java        # 测试控制器
│   ├── service/                       # 服务层
│   │   ├── UserService.java           # 用户服务
│   │   └── DiaryService.java          # 日记服务
│   ├── mapper/                        # 数据访问层
│   │   ├── UserMapper.java            # 用户Mapper
│   │   └── DiaryMapper.java           # 日记Mapper
│   ├── entity/                        # 实体类
│   │   ├── User.java                  # 用户实体
│   │   ├── Diary.java                 # 日记实体
│   │   ├── Like.java                  # 点赞实体
│   │   └── Comment.java               # 评论实体
│   ├── dto/                           # 数据传输对象
│   │   ├── ApiResponse.java           # 统一响应格式
│   │   ├── LoginRequest.java          # 登录请求
│   │   ├── RegisterRequest.java       # 注册请求
│   │   └── DiaryRequest.java          # 日记请求
│   └── util/                          # 工具类
│       ├── JwtUtil.java               # JWT工具
│       └── PasswordUtil.java          # 密码加密工具
├── src/main/resources/
│   ├── application.yml                # 应用配置
│   └── mapper/                        # MyBatis映射文件
│       └── DiaryMapper.xml            # 日记Mapper XML
├── database/
│   └── init.sql                       # 数据库初始化脚本
└── pom.xml                            # Maven配置
```

## 数据库设计

### 用户表 (users)
- id: 主键
- username: 用户名（唯一）
- password: 密码（加密）
- nickname: 用户昵称
- avatar_url: 头像URL
- email: 邮箱
- gender: 性别
- country/province/city: 地区信息
- status: 用户状态
- created_at/updated_at: 时间戳

### 日记表 (diaries)
- id: 主键
- user_id: 用户ID（外键）
- title: 日记标题
- content: 日记内容
- mood: 心情评分
- weather: 天气
- location: 位置
- is_public: 是否公开
- view_count: 浏览次数
- like_count: 点赞数
- created_at/updated_at: 时间戳

### 点赞表 (likes)
- id: 主键
- user_id: 点赞用户ID
- diary_id: 日记ID
- created_at: 点赞时间

### 评论表 (comments)
- id: 主键
- user_id: 评论用户ID
- diary_id: 日记ID
- content: 评论内容
- parent_id: 父评论ID（回复功能）
- created_at: 评论时间

## API接口

### 用户相关
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息

### 日记相关
- `POST /api/diary` - 创建日记
- `GET /api/diary/{id}` - 获取日记详情
- `GET /api/diary/my` - 获取我的日记列表
- `GET /api/diary/public` - 获取公开日记列表
- `PUT /api/diary/{id}` - 更新日记
- `DELETE /api/diary/{id}` - 删除日记

### 测试接口
- `GET /api/test/hello` - 测试接口

## 安装和运行

### 1. 环境要求
- JDK 8 或更高版本
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库配置
```sql
-- 执行数据库初始化脚本
mysql -u root -p < database/init.sql
```

### 3. 应用配置
修改 `src/main/resources/application.yml` 中的数据库连接信息：
```yaml
spring:
  datasource:
    url: ************************************
    username: your_username
    password: your_password
```

### 4. 启动应用
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

应用将在 `http://localhost:8080` 启动。

## 开发进度

- ✅ 项目框架搭建
- ✅ 数据库设计和初始化
- ✅ 用户注册登录功能
- ✅ 日记CRUD功能
- 🔄 点赞和评论功能（待完成）
- 🔄 安全配置（待完成）
- 🔄 API测试（待完成）

## 注意事项

1. 确保MySQL服务正在运行
2. 在生产环境中修改JWT密钥
3. 根据需要配置Redis（可选）
4. 默认测试账号：
   - 用户名：admin，密码：123456
   - 用户名：testuser，密码：123456

## 许可证

MIT License 