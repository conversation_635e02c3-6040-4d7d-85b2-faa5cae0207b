apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-config
  namespace: diary-app
data:
  my.cnf: |
    [mysqld]
    default-authentication-plugin=mysql_native_password
    bind-address=0.0.0.0
    max_connections=1000
    innodb_buffer_pool_size=256M
    character-set-server=utf8mb4
    collation-server=utf8mb4_unicode_ci
    
    [mysql]
    default-character-set=utf8mb4
    
    [client]
    default-character-set=utf8mb4
