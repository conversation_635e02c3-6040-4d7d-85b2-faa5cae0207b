#!/bin/bash

set -e

echo "========================================="
echo "开始部署日记应用到Kubernetes集群"
echo "========================================="

# 创建命名空间
echo "1. 创建命名空间..."
kubectl apply -f namespace.yaml

# 等待命名空间创建完成
sleep 2

# 部署MySQL
echo "2. 部署MySQL数据库..."
kubectl apply -f mysql-secret.yaml
kubectl apply -f mysql-configmap.yaml
kubectl apply -f mysql-service.yaml
kubectl apply -f mysql-statefulset.yaml

# 等待MySQL启动
echo "等待MySQL启动..."
kubectl wait --for=condition=ready pod -l app=mysql -n diary-app --timeout=300s

# 初始化数据库
echo "3. 初始化数据库..."
kubectl exec -n diary-app mysql-0 -- mysql -u root -proot -e "CREATE DATABASE IF NOT EXISTS diary_db;"

# 构建后端镜像
echo "4. 构建后端镜像..."
cd /root/diary-app/backend
docker build -f ../k8s-deploy/backend-dockerfile -t diary-backend:latest .

# 部署后端
echo "5. 部署后端服务..."
cd /root/diary-app/k8s-deploy
kubectl apply -f backend-configmap.yaml
kubectl apply -f backend-deployment.yaml

# 等待后端启动
echo "等待后端服务启动..."
kubectl wait --for=condition=available deployment/diary-backend -n diary-app --timeout=300s

# 构建前端镜像
echo "6. 构建前端镜像..."
cd /root/diary-app/frontend
cp ../k8s-deploy/nginx.conf .
docker build -f ../k8s-deploy/frontend-dockerfile -t diary-frontend:latest .

# 部署前端
echo "7. 部署前端服务..."
cd /root/diary-app/k8s-deploy
kubectl apply -f frontend-deployment.yaml

# 等待前端启动
echo "等待前端服务启动..."
kubectl wait --for=condition=available deployment/diary-frontend -n diary-app --timeout=300s

# 部署Ingress
echo "8. 部署Ingress..."
kubectl apply -f ingress.yaml

# 部署HPA
echo "9. 配置自动扩缩容..."
kubectl apply -f hpa.yaml

echo "========================================="
echo "部署完成！"
echo "========================================="

# 显示部署状态
echo "部署状态："
kubectl get all -n diary-app

echo ""
echo "Ingress信息："
kubectl get ingress -n diary-app

echo ""
echo "HPA状态："
kubectl get hpa -n diary-app

echo ""
echo "访问地址："
echo "请在本地hosts文件中添加："
echo "$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="ExternalIP")].address}') diary.local"
echo "然后访问: http://diary.local"
