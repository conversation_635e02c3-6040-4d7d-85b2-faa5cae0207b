import React, { useEffect } from 'react';
import { Form, Input, Button, Card, message, Spin, Select } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, Link } from 'react-router-dom';
import { registerAsync, clearError } from '../store/slices/authSlice';
import { GENDER_OPTIONS } from '../utils/constants';
import './Register.css';

const { Option } = Select;

const Register = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error, isAuthenticated } = useSelector((state) => state.auth);

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    if (error) {
      message.error(error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  const onFinish = async (values) => {
    try {
      await dispatch(registerAsync(values)).unwrap();
      message.success('注册成功！请登录');
      navigate('/login');
    } catch (error) {
      // 错误已在useEffect中处理
    }
  };

  return (
    <div className="register-container">
      <Card className="register-card" title="注册日记应用">
        <Spin spinning={loading}>
          <Form
            form={form}
            name="register"
            onFinish={onFinish}
            autoComplete="off"
            size="large"
            scrollToFirstError
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名!' },
                { min: 3, max: 20, message: '用户名长度必须在3-20个字符之间!' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码!' },
                { min: 6, max: 20, message: '密码长度必须在6-20个字符之间!' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码!' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致!'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="确认密码"
              />
            </Form.Item>

            <Form.Item
              name="nickname"
              rules={[
                { required: true, message: '请输入昵称!' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="昵称"
              />
            </Form.Item>

            <Form.Item
              name="email"
              rules={[
                { type: 'email', message: '请输入有效的邮箱地址!' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="邮箱（可选）"
              />
            </Form.Item>

            <Form.Item name="gender">
              <Select placeholder="选择性别（可选）">
                {GENDER_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="country">
              <Input placeholder="国家（可选）" />
            </Form.Item>

            <Form.Item name="province">
              <Input placeholder="省份（可选）" />
            </Form.Item>

            <Form.Item name="city">
              <Input placeholder="城市（可选）" />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" block>
                注册
              </Button>
            </Form.Item>

            <div className="register-footer">
              已有账号？ <Link to="/login">立即登录</Link>
            </div>
          </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default Register;
