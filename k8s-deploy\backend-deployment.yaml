apiVersion: apps/v1
kind: Deployment
metadata:
  name: diary-backend
  namespace: diary-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: diary-backend
  template:
    metadata:
      labels:
        app: diary-backend
    spec:
      containers:
      - name: diary-backend
        image: diary-backend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        livenessProbe:
          httpGet:
            path: /api/test/hello
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/test/hello
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: config-volume
        configMap:
          name: backend-config

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: diary-app
spec:
  selector:
    app: diary-backend
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
