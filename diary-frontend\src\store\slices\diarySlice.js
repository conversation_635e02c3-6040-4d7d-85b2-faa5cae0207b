import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  getPublicDiaries,
  getMyDiaries,
  getDiaryDetail,
  createDiary,
  updateDiary,
  deleteDiary,
} from '../../services/diaryService';

// 异步action - 获取公开日记列表
export const getPublicDiariesAsync = createAsyncThunk(
  'diary/getPublicDiaries',
  async (params, { rejectWithValue }) => {
    try {
      const response = await getPublicDiaries(params);
      if (response.code === 200) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取公开日记失败');
    }
  }
);

// 异步action - 获取我的日记列表
export const getMyDiariesAsync = createAsyncThunk(
  'diary/getMyDiaries',
  async (params, { rejectWithValue }) => {
    try {
      const response = await getMyDiaries(params);
      if (response.code === 200) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取我的日记失败');
    }
  }
);

// 异步action - 获取日记详情
export const getDiaryDetailAsync = createAsyncThunk(
  'diary/getDiaryDetail',
  async (id, { rejectWithValue }) => {
    try {
      const response = await getDiaryDetail(id);
      if (response.code === 200) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取日记详情失败');
    }
  }
);

// 异步action - 创建日记
export const createDiaryAsync = createAsyncThunk(
  'diary/createDiary',
  async (diaryData, { rejectWithValue }) => {
    try {
      const response = await createDiary(diaryData);
      if (response.code === 200) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '创建日记失败');
    }
  }
);

// 异步action - 更新日记
export const updateDiaryAsync = createAsyncThunk(
  'diary/updateDiary',
  async ({ id, diaryData }, { rejectWithValue }) => {
    try {
      const response = await updateDiary(id, diaryData);
      if (response.code === 200) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '更新日记失败');
    }
  }
);

// 异步action - 删除日记
export const deleteDiaryAsync = createAsyncThunk(
  'diary/deleteDiary',
  async (id, { rejectWithValue }) => {
    try {
      const response = await deleteDiary(id);
      if (response.code === 200) {
        return id;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '删除日记失败');
    }
  }
);

const diarySlice = createSlice({
  name: 'diary',
  initialState: {
    publicDiaries: {
      records: [],
      total: 0,
      current: 1,
      size: 10,
    },
    myDiaries: {
      records: [],
      total: 0,
      current: 1,
      size: 10,
    },
    currentDiary: null,
    loading: false,
    error: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentDiary: (state) => {
      state.currentDiary = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取公开日记列表
      .addCase(getPublicDiariesAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getPublicDiariesAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.publicDiaries = action.payload;
      })
      .addCase(getPublicDiariesAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 获取我的日记列表
      .addCase(getMyDiariesAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMyDiariesAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.myDiaries = action.payload;
      })
      .addCase(getMyDiariesAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 获取日记详情
      .addCase(getDiaryDetailAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDiaryDetailAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.currentDiary = action.payload;
      })
      .addCase(getDiaryDetailAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 创建日记
      .addCase(createDiaryAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createDiaryAsync.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(createDiaryAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 更新日记
      .addCase(updateDiaryAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateDiaryAsync.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateDiaryAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 删除日记
      .addCase(deleteDiaryAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteDiaryAsync.fulfilled, (state, action) => {
        state.loading = false;
        // 从列表中移除已删除的日记
        state.myDiaries.records = state.myDiaries.records.filter(
          diary => diary.id !== action.payload
        );
      })
      .addCase(deleteDiaryAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, clearCurrentDiary } = diarySlice.actions;
export default diarySlice.reducer;
