import React, { useEffect } from 'react';
import { 
  Card, 
  Button, 
  Tag, 
  Space, 
  Spin, 
  Popconfirm, 
  message 
} from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  ArrowLeftOutlined,
  EyeOutlined,
  HeartOutlined 
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  getDiaryDetailAsync, 
  deleteDiaryAsync, 
  clearCurrentDiary 
} from '../store/slices/diarySlice';
import { MOOD_OPTIONS } from '../utils/constants';
import dayjs from 'dayjs';
import './DiaryDetail.css';

const DiaryDetail = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const { currentDiary, loading } = useSelector((state) => state.diary);
  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    if (id) {
      dispatch(getDiaryDetailAsync(id));
    }

    return () => {
      dispatch(clearCurrentDiary());
    };
  }, [dispatch, id]);

  const handleDelete = async () => {
    try {
      await dispatch(deleteDiaryAsync(id)).unwrap();
      message.success('删除成功');
      navigate('/my-diaries');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  const getMoodInfo = (mood) => {
    return MOOD_OPTIONS.find(option => option.value === mood) || {};
  };

  if (loading) {
    return (
      <div className="diary-detail-loading">
        <Spin size="large" />
      </div>
    );
  }

  if (!currentDiary) {
    return (
      <div className="diary-detail-error">
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <p>日记不存在或已被删除</p>
            <Button onClick={handleBack}>返回</Button>
          </div>
        </Card>
      </div>
    );
  }

  const isOwner = user && user.id === currentDiary.userId;
  const moodInfo = getMoodInfo(currentDiary.mood);

  return (
    <div className="diary-detail">
      <Card
        title={
          <div className="diary-header">
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBack}
              type="text"
            >
              返回
            </Button>
            <span className="diary-title">
              {currentDiary.title || '无标题'}
            </span>
          </div>
        }
        extra={
          isOwner && (
            <Space>
              <Button
                icon={<EditOutlined />}
                onClick={() => navigate(`/diary/edit/${id}`)}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这篇日记吗？"
                onConfirm={handleDelete}
                okText="确定"
                cancelText="取消"
              >
                <Button danger icon={<DeleteOutlined />}>
                  删除
                </Button>
              </Popconfirm>
            </Space>
          )
        }
      >
        <div className="diary-meta">
          <Space wrap>
            <Tag color={currentDiary.isPublic ? 'green' : 'orange'}>
              {currentDiary.isPublic ? '公开' : '私密'}
            </Tag>
            {moodInfo.label && (
              <Tag color={moodInfo.color}>
                {moodInfo.label}
              </Tag>
            )}
            {currentDiary.weather && (
              <Tag>天气：{currentDiary.weather}</Tag>
            )}
            {currentDiary.location && (
              <Tag>位置：{currentDiary.location}</Tag>
            )}
          </Space>
        </div>

        <div className="diary-content">
          {currentDiary.content}
        </div>

        <div className="diary-footer">
          <div className="diary-stats">
            <Space split={<span>•</span>}>
              <span>
                <EyeOutlined /> {currentDiary.viewCount} 次浏览
              </span>
              <span>
                <HeartOutlined /> {currentDiary.likeCount} 个赞
              </span>
              <span>
                发布于 {dayjs(currentDiary.createdAt).format('YYYY年MM月DD日 HH:mm')}
              </span>
              {currentDiary.updatedAt !== currentDiary.createdAt && (
                <span>
                  更新于 {dayjs(currentDiary.updatedAt).format('YYYY年MM月DD日 HH:mm')}
                </span>
              )}
            </Space>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DiaryDetail;
