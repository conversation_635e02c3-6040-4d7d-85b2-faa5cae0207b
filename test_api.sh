#!/bin/bash
set -e

API_URL="http://localhost:8080/api"
USERNAME="apitestuser"
PASSWORD="123456"

# 1. 注册
curl -s -X POST "$API_URL/users/register" \
  -H "Content-Type: application/json" \
  -d '{"username":"'$USERNAME'","password":"'$PASSWORD'"}'
echo -e "\n[注册完成]"

# 2. 登录获取 token
LOGIN_RESP=$(curl -s -X POST "$API_URL/users/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"'$USERNAME'","password":"'$PASSWORD'"}')
echo "$LOGIN_RESP"
TOKEN=$(echo $LOGIN_RESP | grep -o '"token":"[^"]*"' | cut -d '"' -f4)
echo "TOKEN: $TOKEN"

# 3. 获取用户信息
curl -s -X GET "$API_URL/users/profile" \
  -H "Authorization: Bearer $TOKEN"
echo -e "\n[获取用户信息完成]"

# 4. 更新用户信息
curl -s -X PUT "$API_URL/users/profile" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"nickname":"新昵称","email":"<EMAIL>","gender":1,"country":"中国","province":"上海","city":"上海"}'
echo -e "\n[更新用户信息完成]"

# 5. 获取公开日记
curl -s -X GET "$API_URL/diaries/public?page=1&size=10"
echo -e "\n[获取公开日记完成]"

# 6. 创建日记
CREATE_RESP=$(curl -s -X POST "$API_URL/diaries" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title":"测试日记","content":"今天很开心！","mood":5,"weather":"晴天","location":"北京","isPublic":1}')
echo "$CREATE_RESP"
DIARY_ID=$(echo $CREATE_RESP | grep -o '"id":[0-9]*' | head -1 | grep -o '[0-9]*')
echo "DIARY_ID: $DIARY_ID"
echo -e "\n[创建日记完成]"

# 7. 获取我的日记
curl -s -X GET "$API_URL/diaries/my?page=1&size=10" \
  -H "Authorization: Bearer $TOKEN"
echo -e "\n[获取我的日记完成]"

# 8. 获取指定日记
curl -s -X GET "$API_URL/diaries/$DIARY_ID" \
  -H "Authorization: Bearer $TOKEN"
echo -e "\n[获取指定日记完成]"

# 9. 更新日记
curl -s -X PUT "$API_URL/diaries/$DIARY_ID" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title":"更新后的日记标题","content":"更新内容","mood":4,"weather":"多云","location":"上海","isPublic":1}'
echo -e "\n[更新日记完成]"

# 10. 删除日记
curl -s -X DELETE "$API_URL/diaries/$DIARY_ID" \
  -H "Authorization: Bearer $TOKEN"
echo -e "\n[删除日记完成]"

echo "\n所有接口自动化测试完成！" 