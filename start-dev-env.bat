@echo off
echo ========================================
echo           启动开发环境
echo ========================================
echo.

echo 1. 启动MySQL Docker容器...
docker start mysql-dev
if %errorlevel% neq 0 (
    echo MySQL容器启动失败！
    pause
    exit /b 1
)

echo 等待MySQL完全启动...
timeout /t 5 /nobreak > nul

echo 2. 检查MySQL连接...
mysql -h 127.0.0.1 -P 3306 -u root -proot -e "SELECT 'MySQL连接成功' as status;" > nul 2>&1
if %errorlevel% neq 0 (
    echo MySQL连接失败！
    pause
    exit /b 1
)

echo 3. 启动后端服务...
cd /d "d:\wx-kaifazhegongju\my-backgroud-try"
start "后端服务" cmd /k "echo 启动Spring Boot后端... && mvn spring-boot:run"

echo 4. 启动前端服务...
cd /d "d:\wx-kaifazhegongju\my-backgroud-try\diary-frontend"
start "前端服务" cmd /k "echo 启动React前端... && npm run dev"

echo.
echo ========================================
echo       开发环境启动完成！
echo ========================================
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:8080
echo MySQL:   127.0.0.1:3306
echo ========================================
pause
