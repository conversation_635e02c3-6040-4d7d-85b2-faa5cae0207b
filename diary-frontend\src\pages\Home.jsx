import React, { useEffect, useState } from 'react';
import { 
  Card, 
  List, 
  Button, 
  Empty, 
  Tag, 
  Space,
  Pagination,
  Avatar 
} from 'antd';
import { 
  EyeOutlined,
  HeartOutlined,
  UserOutlined 
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getPublicDiariesAsync } from '../store/slices/diarySlice';
import { MOOD_OPTIONS } from '../utils/constants';
import dayjs from 'dayjs';
import './Home.css';

const Home = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { publicDiaries, loading } = useSelector((state) => state.diary);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    fetchPublicDiaries();
  }, [currentPage, pageSize]);

  const fetchPublicDiaries = () => {
    dispatch(getPublicDiariesAsync({ current: currentPage, size: pageSize }));
  };

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const getMoodInfo = (mood) => {
    return MOOD_OPTIONS.find(option => option.value === mood) || {};
  };

  const renderDiaryItem = (item) => {
    const moodInfo = getMoodInfo(item.mood);
    
    return (
      <List.Item
        key={item.id}
        actions={[
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/diary/${item.id}`)}
          >
            阅读全文
          </Button>,
        ]}
      >
        <List.Item.Meta
          avatar={<Avatar icon={<UserOutlined />} />}
          title={
            <div className="diary-title">
              <span>{item.title || '无标题'}</span>
              <Space>
                {moodInfo.label && (
                  <Tag color={moodInfo.color}>
                    {moodInfo.label}
                  </Tag>
                )}
                {item.weather && (
                  <Tag>{item.weather}</Tag>
                )}
              </Space>
            </div>
          }
          description={
            <div className="diary-meta">
              <div className="diary-content">
                {item.content.length > 200 
                  ? `${item.content.substring(0, 200)}...` 
                  : item.content
                }
              </div>
              <div className="diary-info">
                <Space split={<span>•</span>}>
                  <span>{dayjs(item.createdAt).format('MM-DD HH:mm')}</span>
                  {item.location && <span>{item.location}</span>}
                  <span><EyeOutlined /> {item.viewCount}</span>
                  <span><HeartOutlined /> {item.likeCount}</span>
                </Space>
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <div className="home">
      <div className="home-header">
        <Card className="welcome-card">
          <div className="welcome-content">
            <h2>📖 欢迎来到日记世界</h2>
            <p>在这里，你可以记录生活的点点滴滴，也可以阅读他人分享的精彩故事</p>
            <Button 
              type="primary" 
              size="large"
              onClick={() => navigate('/diary/new')}
            >
              开始写日记
            </Button>
          </div>
        </Card>
      </div>

      <Card title="📚 公开日记" className="public-diaries-card">
        {publicDiaries.records.length === 0 ? (
          <Empty
            description="还没有公开的日记"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <>
            <List
              loading={loading}
              dataSource={publicDiaries.records}
              renderItem={renderDiaryItem}
              pagination={false}
            />
            
            <div className="pagination-wrapper">
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={publicDiaries.total}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
                onChange={handlePageChange}
              />
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default Home;
