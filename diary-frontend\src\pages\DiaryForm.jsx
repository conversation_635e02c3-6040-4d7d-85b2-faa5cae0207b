import React, { useEffect, useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Select, 
  Radio, 
  message, 
  Spin 
} from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  createDiaryAsync, 
  updateDiaryAsync, 
  getDiaryDetailAsync,
  clearCurrentDiary 
} from '../store/slices/diarySlice';
import { MOOD_OPTIONS, WEATHER_OPTIONS } from '../utils/constants';
import './DiaryForm.css';

const { TextArea } = Input;
const { Option } = Select;

const DiaryForm = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const { currentDiary, loading } = useSelector((state) => state.diary);
  const [submitting, setSubmitting] = useState(false);

  const isEdit = !!id;

  useEffect(() => {
    if (isEdit) {
      dispatch(getDiaryDetailAsync(id));
    } else {
      dispatch(clearCurrentDiary());
    }

    return () => {
      dispatch(clearCurrentDiary());
    };
  }, [dispatch, id, isEdit]);

  useEffect(() => {
    if (isEdit && currentDiary) {
      form.setFieldsValue({
        title: currentDiary.title,
        content: currentDiary.content,
        mood: currentDiary.mood,
        weather: currentDiary.weather,
        location: currentDiary.location,
        isPublic: currentDiary.isPublic,
      });
    }
  }, [currentDiary, form, isEdit]);

  const onFinish = async (values) => {
    setSubmitting(true);
    try {
      if (isEdit) {
        await dispatch(updateDiaryAsync({ id, diaryData: values })).unwrap();
        message.success('更新成功');
      } else {
        await dispatch(createDiaryAsync(values)).unwrap();
        message.success('创建成功');
      }
      navigate('/my-diaries');
    } catch (error) {
      message.error(isEdit ? '更新失败' : '创建失败');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/my-diaries');
  };

  if (isEdit && loading) {
    return (
      <div className="diary-form-loading">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="diary-form">
      <Card title={isEdit ? '编辑日记' : '写新日记'}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            isPublic: 0,
            mood: 3,
          }}
        >
          <Form.Item
            name="title"
            label="标题"
          >
            <Input placeholder="给你的日记起个标题吧..." />
          </Form.Item>

          <Form.Item
            name="content"
            label="内容"
            rules={[
              { required: true, message: '请输入日记内容' },
            ]}
          >
            <TextArea
              rows={10}
              placeholder="记录今天发生的事情..."
              showCount
              maxLength={5000}
            />
          </Form.Item>

          <div className="form-row">
            <Form.Item
              name="mood"
              label="心情"
              className="form-item-half"
            >
              <Select placeholder="选择今天的心情">
                {MOOD_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="weather"
              label="天气"
              className="form-item-half"
            >
              <Select placeholder="今天的天气">
                {WEATHER_OPTIONS.map(weather => (
                  <Option key={weather} value={weather}>
                    {weather}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="location"
            label="位置"
          >
            <Input placeholder="你在哪里写下这篇日记？" />
          </Form.Item>

          <Form.Item
            name="isPublic"
            label="可见性"
          >
            <Radio.Group>
              <Radio value={0}>私密（只有自己可见）</Radio>
              <Radio value={1}>公开（所有人可见）</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item className="form-actions">
            <Button onClick={handleCancel} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={submitting}
            >
              {isEdit ? '更新' : '发布'}
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default DiaryForm;
