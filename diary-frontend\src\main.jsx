import React from "react";
import { createRoot } from "react-dom/client";
import { Provider } from "react-redux";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { ConfigProvider } from "antd";
import zhCN from "antd/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import store from "./store";
import App from "./App.jsx";
import "./index.css";

// 设置dayjs为中文
dayjs.locale("zh-cn");

createRoot(document.getElementById("root")).render(
  <Provider store={store}>
    <BrowserRouter>
      <ConfigProvider locale={zhCN}>
        <App />
      </ConfigProvider>
    </BrowserRouter>
  </Provider>
);
