package com.diary.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class PasswordTestUtil {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "123456";
        String encodedPassword = encoder.encode(password);
        // 以下打印仅供测试使用
        System.out.println("Password: " + password);
        System.out.println("Encoded: " + encodedPassword);
        
        // 验证
        boolean matches = encoder.matches(password, encodedPassword);
        System.out.println("Matches: " + matches);
        
        // 验证已知的哈希值
        String knownHash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa";
        boolean knownMatches = encoder.matches(password, knownHash);
        System.out.println("Known hash matches: " + knownMatches);
    }
} 