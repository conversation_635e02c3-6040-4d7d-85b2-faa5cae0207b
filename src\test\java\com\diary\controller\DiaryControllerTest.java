package com.diary.controller;

import com.diary.dto.DiaryRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.*;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc(addFilters = true)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class DiaryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private static String testUsername = "apitestuser";
    private static String testPassword = "123456";
    private static String token;
    private static Long diaryId;

    @BeforeAll
    public static void beforeAll() {
        // token和diaryId初始化在测试中完成
    }

    @Test
    @Order(1)
    public void testLogin() throws Exception {
        String loginJson = String.format("{\"username\":\"%s\",\"password\":\"%s\"}", testUsername, testPassword);
        MvcResult result = mockMvc.perform(post("/user/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(loginJson))
                .andReturn();
        String response = result.getResponse().getContentAsString();
        int code = objectMapper.readTree(response).path("code").asInt();
        if (code != 200) {
            // 自动注册
            String registerJson = String.format("{\"username\":\"%s\",\"password\":\"%s\",\"confirmPassword\":\"%s\",\"nickname\":\"接口测试用户\",\"email\":\"<EMAIL>\",\"gender\":1,\"country\":\"中国\",\"province\":\"北京\",\"city\":\"北京\"}", testUsername, testPassword, testPassword);
            mockMvc.perform(post("/user/register")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(registerJson))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
            // 再次登录
            result = mockMvc.perform(post("/user/login")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(loginJson))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();
            response = result.getResponse().getContentAsString();
        } else {
            // 登录成功
            Assertions.assertEquals(200, code);
        }
        token = objectMapper.readTree(response).path("data").path("token").asText();
        Assertions.assertNotNull(token);
    }

    @Test
    @Order(2)
    public void testGetPublicDiaries() throws Exception {
        mockMvc.perform(get("/diary/public"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(3)
    public void testCreateDiary() throws Exception {
        if (token == null) testLogin();
        DiaryRequest req = new DiaryRequest();
        req.setTitle("测试日记");
        req.setContent("今天很开心！");
        req.setMood(5);
        req.setWeather("晴天");
        req.setLocation("北京");
        req.setIsPublic(1);
        MvcResult result = mockMvc.perform(post("/diary")
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(req)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();
        String response = result.getResponse().getContentAsString();
        diaryId = objectMapper.readTree(response).path("data").path("id").asLong();
        Assertions.assertNotNull(diaryId);
    }

    @Test
    @Order(4)
    public void testGetMyDiaries() throws Exception {
        if (token == null) testLogin();
        mockMvc.perform(get("/diary/my")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(5)
    public void testGetDiaryDetail() throws Exception {
        if (token == null) testLogin();
        if (diaryId == null) testCreateDiary();
        mockMvc.perform(get("/diary/" + diaryId)
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(diaryId));
    }

    @Test
    @Order(6)
    public void testUpdateDiary() throws Exception {
        if (token == null) testLogin();
        if (diaryId == null) testCreateDiary();
        DiaryRequest req = new DiaryRequest();
        req.setTitle("更新后的日记标题");
        req.setContent("更新内容");
        req.setMood(4);
        req.setWeather("多云");
        req.setLocation("上海");
        req.setIsPublic(1);
        mockMvc.perform(put("/diary/" + diaryId)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(req)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("更新后的日记标题"));
    }

    @Test
    @Order(7)
    public void testDeleteDiary() throws Exception {
        if (token == null) testLogin();
        if (diaryId == null) testCreateDiary();
        mockMvc.perform(delete("/diary/" + diaryId)
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
} 