$API_URL = "http://localhost:8080/api"
$USERNAME = "apitestuser"
$PASSWORD = "123456"

Write-Host "1. Register user"
$register = curl -s -X POST "$API_URL/users/register" -H "Content-Type: application/json" -d '{"username":"'+$USERNAME+'","password":"'+$PASSWORD+'"}'
Write-Host $register
Write-Host "[Register done]`n"

Write-Host "2. Login and get token"
$loginResp = curl -s -X POST "$API_URL/users/login" -H "Content-Type: application/json" -d '{"username":"'+$USERNAME+'","password":"'+$PASSWORD+'"}'
Write-Host $loginResp
$token = ($loginResp | ConvertFrom-Json).token
Write-Host "TOKEN: $token`n"

Write-Host "3. Get user profile"
$profile = curl -s -X GET "$API_URL/users/profile" -H "Authorization: Bearer $token"
Write-Host $profile
Write-Host "[Get profile done]`n"

Write-Host "4. Update user profile"
$updateProfile = curl -s -X PUT "$API_URL/users/profile" -H "Authorization: Bearer $token" -H "Content-Type: application/json" -d '{"nickname":"new_nickname","email":"<EMAIL>","gender":1,"country":"China","province":"Shanghai","city":"Shanghai"}'
Write-Host $updateProfile
Write-Host "[Update profile done]`n"

Write-Host "5. Get public diaries"
$publicDiaries = curl -s -X GET "$API_URL/diaries/public?page=1&size=10"
Write-Host $publicDiaries
Write-Host "[Get public diaries done]`n"

Write-Host "6. Create diary"
$createResp = curl -s -X POST "$API_URL/diaries" -H "Authorization: Bearer $token" -H "Content-Type: application/json" -d '{"title":"Test Diary","content":"Happy day!","mood":5,"weather":"Sunny","location":"Beijing","isPublic":1}'
Write-Host $createResp
$diaryId = ($createResp | ConvertFrom-Json).data.id
Write-Host "DIARY_ID: $diaryId`n[Create diary done]`n"

Write-Host "7. Get my diaries"
$myDiaries = curl -s -X GET "$API_URL/diaries/my?page=1&size=10" -H "Authorization: Bearer $token"
Write-Host $myDiaries
Write-Host "[Get my diaries done]`n"

Write-Host "8. Get diary by id"
$getDiary = curl -s -X GET "$API_URL/diaries/$diaryId" -H "Authorization: Bearer $token"
Write-Host $getDiary
Write-Host "[Get diary by id done]`n"

Write-Host "9. Update diary"
$updateDiary = curl -s -X PUT "$API_URL/diaries/$diaryId" -H "Authorization: Bearer $token" -H "Content-Type: application/json" -d '{"title":"Updated Diary","content":"Updated content","mood":4,"weather":"Cloudy","location":"Shanghai","isPublic":1}'
Write-Host $updateDiary
Write-Host "[Update diary done]`n"

Write-Host "10. Delete diary"
$deleteDiary = curl -s -X DELETE "$API_URL/diaries/$diaryId" -H "Authorization: Bearer $token"
Write-Host $deleteDiary
Write-Host "[Delete diary done]`n"

Write-Host "All API tests completed!" 