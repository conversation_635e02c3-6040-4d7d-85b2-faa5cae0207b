package com.diary.controller;

import com.diary.dto.RegisterRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.*;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc(addFilters = true)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private static String testUsername = "apitestuser";
    private static String testPassword = "123456";
    private static String token;

    @Test
    @Order(1)
    public void testRegister() throws Exception {
        // 先尝试登录，若用户已存在则跳过注册
        String loginJson = String.format("{\"username\":\"%s\",\"password\":\"%s\"}", testUsername, testPassword);
        MvcResult loginResult = mockMvc.perform(post("/user/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(loginJson))
                .andReturn();
        String loginResponse = loginResult.getResponse().getContentAsString();
        int loginCode = objectMapper.readTree(loginResponse).path("code").asInt();
        if (loginCode == 200) {
            // 用户已存在，跳过注册
            return;
        }
        RegisterRequest req = new RegisterRequest();
        req.setUsername(testUsername);
        req.setPassword(testPassword);
        req.setConfirmPassword(testPassword);
        req.setNickname("接口测试用户");
        req.setEmail("<EMAIL>");
        req.setGender(1);
        req.setCountry("中国");
        req.setProvince("北京");
        req.setCity("北京");

        mockMvc.perform(post("/user/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(req)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(2)
    public void testLogin() throws Exception {
        String loginJson = String.format("{\"username\":\"%s\",\"password\":\"%s\"}", testUsername, testPassword);
        MvcResult result = mockMvc.perform(post("/user/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(loginJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();
        String response = result.getResponse().getContentAsString();
        token = objectMapper.readTree(response).path("data").path("token").asText();
        Assertions.assertNotNull(token);
    }

    @Test
    @Order(3)
    public void testGetProfile() throws Exception {
        if (token == null) testLogin();
        mockMvc.perform(get("/user/profile")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.username").value(testUsername));
    }

    @Test
    @Order(4)
    public void testUpdateProfile() throws Exception {
        if (token == null) testLogin();
        String updateJson = "{\"nickname\":\"新昵称\",\"email\":\"<EMAIL>\",\"gender\":1,\"country\":\"中国\",\"province\":\"上海\",\"city\":\"上海\"}";
        mockMvc.perform(put("/user/profile")
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content(updateJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.nickname").value("新昵称"));
    }
} 