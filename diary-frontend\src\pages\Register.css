.register-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 450px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: none;
  max-height: 90vh;
  overflow-y: auto;
}

.register-card .ant-card-head {
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.register-card .ant-card-head-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.register-footer {
  text-align: center;
  margin-top: 16px;
  color: #666;
}

.register-footer a {
  color: #1890ff;
  text-decoration: none;
}

.register-footer a:hover {
  text-decoration: underline;
}
