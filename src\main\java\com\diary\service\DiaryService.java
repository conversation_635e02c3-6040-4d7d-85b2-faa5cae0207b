package com.diary.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.diary.dto.DiaryRequest;
import com.diary.entity.Diary;
import com.diary.entity.User;
import com.diary.mapper.DiaryMapper;
import com.diary.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DiaryService {

    @Autowired
    private DiaryMapper diaryMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 创建日记
     */
    @Transactional
    public Diary createDiary(DiaryRequest request, String username) {
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        Diary diary = new Diary();
        diary.setUserId(user.getId());
        diary.setTitle(request.getTitle());
        diary.setContent(request.getContent());
        diary.setMood(request.getMood());
        diary.setWeather(request.getWeather());
        diary.setLocation(request.getLocation());
        diary.setIsPublic(request.getIsPublic());
        diary.setViewCount(0);
        diary.setLikeCount(0);

        diaryMapper.insert(diary);
        return diary;
    }

    /**
     * 获取日记详情
     */
    public Diary getDiaryById(Long diaryId, String username) {
        Diary diary = diaryMapper.selectById(diaryId);
        if (diary == null) {
            throw new RuntimeException("日记不存在");
        }

        // 如果是私密日记，检查是否是作者本人
        if (diary.getIsPublic() == 0) {
            User user = userService.getUserByUsername(username);
            if (user == null || !user.getId().equals(diary.getUserId())) {
                throw new RuntimeException("无权访问此日记");
            }
        }

        // 增加浏览次数
        diaryMapper.incrementViewCount(diaryId);
        diary.setViewCount(diary.getViewCount() + 1);

        return diary;
    }

    /**
     * 获取我的日记列表
     */
    public IPage<Diary> getMyDiaries(String username, int page, int size) {
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        Page<Diary> pageParam = new Page<>(page, size);
        QueryWrapper<Diary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", user.getId());
        queryWrapper.orderByDesc("created_at");

        return diaryMapper.selectPage(pageParam, queryWrapper);
    }

    /**
     * 获取公开日记列表
     */
    public IPage<Diary> getPublicDiaries(int page, int size) {
        Page<Diary> pageParam = new Page<>(page, size);
        QueryWrapper<Diary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_public", 1);
        queryWrapper.orderByDesc("created_at");

        return diaryMapper.selectPage(pageParam, queryWrapper);
    }

    /**
     * 更新日记
     */
    @Transactional
    public Diary updateDiary(Long diaryId, DiaryRequest request, String username) {
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        Diary diary = diaryMapper.selectById(diaryId);
        if (diary == null) {
            throw new RuntimeException("日记不存在");
        }

        // 检查是否是作者本人
        if (!user.getId().equals(diary.getUserId())) {
            throw new RuntimeException("无权修改此日记");
        }

        diary.setTitle(request.getTitle());
        diary.setContent(request.getContent());
        diary.setMood(request.getMood());
        diary.setWeather(request.getWeather());
        diary.setLocation(request.getLocation());
        diary.setIsPublic(request.getIsPublic());

        diaryMapper.updateById(diary);
        return diary;
    }

    /**
     * 删除日记
     */
    @Transactional
    public void deleteDiary(Long diaryId, String username) {
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        Diary diary = diaryMapper.selectById(diaryId);
        if (diary == null) {
            throw new RuntimeException("日记不存在");
        }

        // 检查是否是作者本人
        if (!user.getId().equals(diary.getUserId())) {
            throw new RuntimeException("无权删除此日记");
        }

        diaryMapper.deleteById(diaryId);
    }

    /**
     * 增加点赞数
     */
    @Transactional
    public void incrementLikeCount(Long diaryId) {
        diaryMapper.incrementLikeCount(diaryId);
    }

    /**
     * 减少点赞数
     */
    @Transactional
    public void decrementLikeCount(Long diaryId) {
        diaryMapper.decrementLikeCount(diaryId);
    }
} 