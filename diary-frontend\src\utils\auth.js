// 认证相关工具函数

// 保存token到localStorage
export const saveToken = (token) => {
  localStorage.setItem('token', token);
};

// 获取token
export const getToken = () => {
  return localStorage.getItem('token');
};

// 移除token
export const removeToken = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

// 保存用户信息
export const saveUser = (user) => {
  localStorage.setItem('user', JSON.stringify(user));
};

// 获取用户信息
export const getUser = () => {
  const user = localStorage.getItem('user');
  return user ? JSON.parse(user) : null;
};

// 检查是否已登录
export const isAuthenticated = () => {
  return !!getToken();
};

// 登出
export const logout = () => {
  removeToken();
  window.location.href = '/login';
};
