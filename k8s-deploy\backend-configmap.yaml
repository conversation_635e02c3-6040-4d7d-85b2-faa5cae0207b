apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: diary-app
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      application:
        name: diary-backend
      
      datasource:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *************************************************************************************************************************
        username: root
        password: root
      
      jpa:
        hibernate:
          ddl-auto: update
        show-sql: false
        properties:
          hibernate:
            dialect: org.hibernate.dialect.MySQL8Dialect
    
    mybatis-plus:
      configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
      global-config:
        db-config:
          logic-delete-field: deleted
          logic-delete-value: 1
          logic-not-delete-value: 0
    
    jwt:
      secret: mySecretKey123456789
      expiration: 86400000
    
    logging:
      level:
        com.diary: debug
