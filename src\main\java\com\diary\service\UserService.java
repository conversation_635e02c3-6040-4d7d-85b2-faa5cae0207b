package com.diary.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.diary.dto.LoginRequest;
import com.diary.dto.RegisterRequest;
import com.diary.entity.User;
import com.diary.mapper.UserMapper;
import com.diary.util.JwtUtil;
import com.diary.util.PasswordUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
public class UserService {

    private final UserMapper userMapper;
    private final JwtUtil jwtUtil;
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    public UserService(UserMapper userMapper, JwtUtil jwtUtil) {
        this.userMapper = userMapper;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 用户登录
     */
    public Map<String, Object> login(LoginRequest request) {
        try {
            // 查找用户
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", request.getUsername());
            User user = userMapper.selectOne(queryWrapper);
            if (user == null) {
                throw new RuntimeException("用户名或密码错误");
            }
            // 验证密码
            if (!PasswordUtil.INSTANCE.matches(request.getPassword(), user.getPassword())) {
                throw new RuntimeException("用户名或密码错误");
            }
            // 检查用户状态
            if (user.getStatus() != null && user.getStatus() == 0) {
                throw new RuntimeException("账户已被禁用");
            }
            // 生成JWT令牌
            String token = jwtUtil.generateToken(user.getUsername());
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("user", user);
            return result;
        } catch (Exception e) {
            logger.error("[login异常] request={}, userMapper={}, jwtUtil={}", request, userMapper, jwtUtil, e);
            throw new RuntimeException("登录异常: " + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @Transactional
    public User register(RegisterRequest request) {
        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new RuntimeException("两次输入的密码不一致");
        }
        
        // 检查用户名是否已存在
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", request.getUsername());
        User existingUser = userMapper.selectOne(queryWrapper);
        if (existingUser != null) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (request.getEmail() != null && !request.getEmail().isEmpty()) {
            queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("email", request.getEmail());
            existingUser = userMapper.selectOne(queryWrapper);
            if (existingUser != null) {
                throw new RuntimeException("邮箱已被使用");
            }
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(PasswordUtil.INSTANCE.encode(request.getPassword()));
        user.setNickname(request.getNickname());
        user.setEmail(request.getEmail());
        user.setGender(request.getGender());
        user.setCountry(request.getCountry());
        user.setProvince(request.getProvince());
        user.setCity(request.getCity());
        user.setStatus(1); // 默认启用
        
        userMapper.insert(user);
        return user;
    }

    /**
     * 根据用户名获取用户信息
     */
    public User getUserByUsername(String username) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        return userMapper.selectOne(queryWrapper);
    }

    /**
     * 根据用户ID获取用户信息
     */
    public User getUserById(Long userId) {
        return userMapper.selectById(userId);
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public User updateUser(Long userId, User userInfo) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        user.setNickname(userInfo.getNickname());
        user.setAvatarUrl(userInfo.getAvatarUrl());
        user.setEmail(userInfo.getEmail());
        user.setGender(userInfo.getGender());
        user.setCountry(userInfo.getCountry());
        user.setProvince(userInfo.getProvince());
        user.setCity(userInfo.getCity());
        
        userMapper.updateById(user);
        return user;
    }
} 