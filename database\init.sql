-- Create database
CREATE DATABASE IF NOT EXISTS diary_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use database
USE diary_db;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT 'Username',
    password VARCHAR(255) NOT NULL COMMENT 'Password (encrypted)',
    nickname VARCHAR(50) COMMENT 'User nickname',
    avatar_url VARCHAR(500) COMMENT 'Avatar URL',
    email VARCHAR(100) COMMENT 'Email address',
    gender TINYINT COMMENT 'Gender 0-unknown 1-male 2-female',
    country VARCHAR(50) COMMENT 'Country',
    province VARCHAR(50) COMMENT 'Province',
    city VARCHAR(50) COMMENT 'City',
    status TINYINT DEFAULT 1 COMMENT 'User status 0-disabled 1-active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Users table';

-- Diaries table
CREATE TABLE IF NOT EXISTS diaries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT 'User ID',
    title VARCHAR(200) COMMENT 'Diary title',
    content TEXT NOT NULL COMMENT 'Diary content',
    mood TINYINT COMMENT 'Mood 1-5',
    weather VARCHAR(50) COMMENT 'Weather',
    location VARCHAR(200) COMMENT 'Location',
    is_public TINYINT DEFAULT 0 COMMENT 'Is public 0-private 1-public',
    view_count INT DEFAULT 0 COMMENT 'View count',
    like_count INT DEFAULT 0 COMMENT 'Like count',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_public (is_public),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Diaries table';

-- Likes table
CREATE TABLE IF NOT EXISTS likes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT 'User ID',
    diary_id BIGINT NOT NULL COMMENT 'Diary ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (diary_id) REFERENCES diaries(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_diary (user_id, diary_id),
    INDEX idx_diary_id (diary_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Likes table';

-- Comments table
CREATE TABLE IF NOT EXISTS comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT 'User ID',
    diary_id BIGINT NOT NULL COMMENT 'Diary ID',
    content TEXT NOT NULL COMMENT 'Comment content',
    parent_id BIGINT COMMENT 'Parent comment ID for replies',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (diary_id) REFERENCES diaries(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    INDEX idx_diary_id (diary_id),
    INDEX idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Comments table';

-- Insert sample data
INSERT INTO users (username, password, nickname, email, gender, country, province, city) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '管理员', '<EMAIL>', 1, '中国', '北京', '北京'),
('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '测试用户', '<EMAIL>', 2, '中国', '上海', '上海');

-- Insert sample diaries
INSERT INTO diaries (user_id, title, content, mood, weather, location, is_public, view_count, like_count) VALUES
(1, 'Beautiful Day', 'Today is a beautiful day, feeling great!', 5, 'Sunny', 'Beijing', 1, 0, 0),
(2, 'Rainy Day', 'It\'s raining today, but I feel peaceful.', 4, 'Rainy', 'Shanghai', 1, 0, 0); 