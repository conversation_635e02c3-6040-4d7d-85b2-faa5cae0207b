.diary-detail {
  max-width: 800px;
  margin: 0 auto;
}

.diary-detail-loading,
.diary-detail-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.diary-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.diary-title {
  font-size: 18px;
  font-weight: 500;
}

.diary-meta {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.diary-content {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
  margin-bottom: 24px;
  min-height: 200px;
}

.diary-footer {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.diary-stats {
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .diary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .diary-content {
    font-size: 14px;
  }
  
  .diary-stats {
    font-size: 12px;
  }
}
