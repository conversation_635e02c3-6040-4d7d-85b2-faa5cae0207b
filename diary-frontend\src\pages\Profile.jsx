import React, { useEffect, useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Select, 
  message, 
  Avatar, 
  Upload,
  Row,
  Col 
} from 'antd';
import { UserOutlined, UploadOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { getUserProfileAsync } from '../store/slices/authSlice';
import { updateUserProfile } from '../services/userService';
import { GENDER_OPTIONS } from '../utils/constants';
import './Profile.css';

const { Option } = Select;

const Profile = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { user, loading } = useSelector((state) => state.auth);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (!user) {
      dispatch(getUserProfileAsync());
    }
  }, [dispatch, user]);

  useEffect(() => {
    if (user) {
      form.setFieldsValue({
        username: user.username,
        nickname: user.nickname,
        email: user.email,
        gender: user.gender,
        country: user.country,
        province: user.province,
        city: user.city,
      });
    }
  }, [user, form]);

  const onFinish = async (values) => {
    setSubmitting(true);
    try {
      const response = await updateUserProfile(values);
      if (response.code === 200) {
        message.success('更新成功');
        dispatch(getUserProfileAsync());
      } else {
        message.error(response.message || '更新失败');
      }
    } catch (error) {
      message.error('更新失败');
    } finally {
      setSubmitting(false);
    }
  };

  const uploadProps = {
    name: 'file',
    action: '/api/upload/avatar',
    headers: {
      authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success('头像上传成功');
        dispatch(getUserProfileAsync());
      } else if (info.file.status === 'error') {
        message.error('头像上传失败');
      }
    },
  };

  return (
    <div className="profile">
      <Row gutter={24}>
        <Col xs={24} md={8}>
          <Card title="个人信息" className="profile-info-card">
            <div className="avatar-section">
              <Avatar 
                size={120} 
                icon={<UserOutlined />}
                src={user?.avatarUrl}
                className="user-avatar"
              />
              <Upload {...uploadProps} showUploadList={false}>
                <Button icon={<UploadOutlined />} className="upload-btn">
                  更换头像
                </Button>
              </Upload>
            </div>
            
            <div className="user-stats">
              <div className="stat-item">
                <div className="stat-label">用户名</div>
                <div className="stat-value">{user?.username}</div>
              </div>
              <div className="stat-item">
                <div className="stat-label">昵称</div>
                <div className="stat-value">{user?.nickname}</div>
              </div>
              <div className="stat-item">
                <div className="stat-label">注册时间</div>
                <div className="stat-value">
                  {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : '-'}
                </div>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} md={16}>
          <Card title="编辑资料" className="profile-edit-card">
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              loading={loading}
            >
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="username"
                    label="用户名"
                  >
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="nickname"
                    label="昵称"
                    rules={[
                      { required: true, message: '请输入昵称' },
                    ]}
                  >
                    <Input placeholder="请输入昵称" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>

              <Form.Item
                name="gender"
                label="性别"
              >
                <Select placeholder="请选择性别">
                  {GENDER_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Row gutter={16}>
                <Col xs={24} sm={8}>
                  <Form.Item
                    name="country"
                    label="国家"
                  >
                    <Input placeholder="请输入国家" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item
                    name="province"
                    label="省份"
                  >
                    <Input placeholder="请输入省份" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item
                    name="city"
                    label="城市"
                  >
                    <Input placeholder="请输入城市" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={submitting}
                  size="large"
                >
                  保存修改
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Profile;
