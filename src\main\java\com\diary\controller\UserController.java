package com.diary.controller;

import com.diary.dto.ApiResponse;
import com.diary.dto.LoginRequest;
import com.diary.dto.RegisterRequest;
import com.diary.entity.User;
import com.diary.service.UserService;
import com.diary.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/user")
@CrossOrigin(origins = "*")
public class UserController {

    private final UserService userService;
    private final JwtUtil jwtUtil;
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    public UserController(UserService userService, JwtUtil jwtUtil) {
        this.userService = userService;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<Map<String, Object>> login(@Valid @RequestBody LoginRequest request) {
        try {
            Map<String, Object> result = userService.login(request);
            return ApiResponse.success("登录成功", result);
        } catch (Exception e) {
            logger.error("用户登录异常", e);
            return ApiResponse.error("用户名或密码错误，或账号异常");
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ApiResponse<User> register(@Valid @RequestBody RegisterRequest request) {
        try {
            User user = userService.register(request);
            return ApiResponse.success("注册成功", user);
        } catch (Exception e) {
            logger.error("用户注册异常", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/profile")
    public ApiResponse<User> getProfile(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getUserByUsername(username);
            if (user == null) {
                return ApiResponse.error("用户不存在");
            }
            // 不返回密码
            user.setPassword(null);
            return ApiResponse.success(user);
        } catch (Exception e) {
            logger.error("获取用户信息异常", e);
            return ApiResponse.error("获取用户信息失败");
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    public ApiResponse<User> updateProfile(@RequestBody User userInfo, HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            String username = jwtUtil.getUsernameFromToken(token);
            User currentUser = userService.getUserByUsername(username);
            if (currentUser == null) {
                return ApiResponse.error("用户不存在");
            }
            
            User updatedUser = userService.updateUser(currentUser.getId(), userInfo);
            updatedUser.setPassword(null);
            return ApiResponse.success("更新成功", updatedUser);
        } catch (Exception e) {
            logger.error("更新用户信息异常", e);
            return ApiResponse.error("更新用户信息失败");
        }
    }

    /**
     * 从请求中获取token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        throw new RuntimeException("未提供有效的认证令牌");
    }
} 