apiVersion: apps/v1
kind: Deployment
metadata:
  name: diary-frontend
  namespace: diary-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: diary-frontend
  template:
    metadata:
      labels:
        app: diary-frontend
    spec:
      containers:
      - name: diary-frontend
        image: diary-frontend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 80
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"

---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: diary-app
spec:
  selector:
    app: diary-frontend
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
