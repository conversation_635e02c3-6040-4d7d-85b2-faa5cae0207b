package com.diary.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.diary.dto.ApiResponse;
import com.diary.dto.DiaryRequest;
import com.diary.entity.Diary;
import com.diary.service.DiaryService;
import com.diary.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/diary")
@CrossOrigin(origins = "*")
public class DiaryController {

    private final DiaryService diaryService;
    private final JwtUtil jwtUtil;
    private static final Logger logger = LoggerFactory.getLogger(DiaryController.class);

    public DiaryController(DiaryService diaryService, JwtUtil jwtUtil) {
        this.diaryService = diaryService;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 创建日记
     */
    @PostMapping
    public ApiResponse<Diary> createDiary(@Valid @RequestBody DiaryRequest request, 
                                        HttpServletRequest httpRequest) {
        try {
            String username = getUsernameFromRequest(httpRequest);
            Diary diary = diaryService.createDiary(request, username);
            return ApiResponse.success("创建成功", diary);
        } catch (Exception e) {
            logger.error("创建日记异常", e);
            return ApiResponse.error("创建日记失败");
        }
    }

    /**
     * 获取日记详情
     */
    @GetMapping("/{id}")
    public ApiResponse<Diary> getDiary(@PathVariable Long id, 
                                     HttpServletRequest httpRequest) {
        try {
            String username = getUsernameFromRequest(httpRequest);
            Diary diary = diaryService.getDiaryById(id, username);
            return ApiResponse.success(diary);
        } catch (Exception e) {
            logger.error("获取日记详情异常", e);
            return ApiResponse.error("获取日记详情失败");
        }
    }

    /**
     * 获取我的日记列表
     */
    @GetMapping("/my")
    public ApiResponse<IPage<Diary>> getMyDiaries(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            HttpServletRequest httpRequest) {
        try {
            String username = getUsernameFromRequest(httpRequest);
            IPage<Diary> diaries = diaryService.getMyDiaries(username, page, size);
            return ApiResponse.success(diaries);
        } catch (Exception e) {
            logger.error("获取我的日记列表异常", e);
            return ApiResponse.error("获取我的日记列表失败");
        }
    }

    /**
     * 获取公开日记列表
     */
    @GetMapping("/public")
    public ApiResponse<IPage<Diary>> getPublicDiaries(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            IPage<Diary> diaries = diaryService.getPublicDiaries(page, size);
            return ApiResponse.success(diaries);
        } catch (Exception e) {
            logger.error("获取公开日记列表异常", e);
            return ApiResponse.error("获取公开日记列表失败");
        }
    }

    /**
     * 更新日记
     */
    @PutMapping("/{id}")
    public ApiResponse<Diary> updateDiary(@PathVariable Long id,
                                        @Valid @RequestBody DiaryRequest request,
                                        HttpServletRequest httpRequest) {
        try {
            String username = getUsernameFromRequest(httpRequest);
            Diary diary = diaryService.updateDiary(id, request, username);
            return ApiResponse.success("更新成功", diary);
        } catch (Exception e) {
            logger.error("更新日记异常", e);
            return ApiResponse.error("更新日记失败");
        }
    }

    /**
     * 删除日记
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteDiary(@PathVariable Long id,
                                       HttpServletRequest httpRequest) {
        try {
            String username = getUsernameFromRequest(httpRequest);
            diaryService.deleteDiary(id, username);
            return ApiResponse.success("删除成功", null);
        } catch (Exception e) {
            logger.error("删除日记异常", e);
            return ApiResponse.error("删除日记失败");
        }
    }

    /**
     * 从请求中获取用户名
     */
    private String getUsernameFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            String token = bearerToken.substring(7);
            return jwtUtil.getUsernameFromToken(token);
        }
        throw new RuntimeException("未提供有效的认证令牌");
    }
} 